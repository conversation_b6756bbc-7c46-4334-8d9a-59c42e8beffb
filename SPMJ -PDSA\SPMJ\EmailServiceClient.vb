Imports System.Net
Imports System.IO
Imports System.Text

''' <summary>
''' Client for communicating with the .NET 9 Email Microservice (.NET 3.5 Compatible)
''' Provides email functionality for .NET 3.5 application
''' </summary>
Public Class EmailServiceClient
    Private ReadOnly _baseUrl As String
    Private ReadOnly _timeout As Integer = 30000 ' 30 seconds
    Private ReadOnly _apiKey As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"

    Public Sub New(baseUrl As String)
        Try
            _baseUrl = baseUrl.TrimEnd("/"c)

            ' Configure HTTP settings for .NET 3.5 to .NET 9 communication
            Try
                ' Set HTTP version and connection settings
                ServicePointManager.Expect100Continue = False
                ServicePointManager.UseNagleAlgorithm = False
                ServicePointManager.DefaultConnectionLimit = 10

                ' Enable SSL/TLS for email service communication (.NET 3.5 compatible)
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 Or SecurityProtocolType.Tls
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT: HTTP/SSL configuration set successfully")
            Catch configEx As Exception
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT: HTTP/SSL configuration warning: " & configEx.Message)
                ' Continue - HTTP should still work for localhost
            End Try

            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT: Initialized with base URL: " & _baseUrl)

            ' Test basic connectivity during initialization
            Try
                Dim testResult As Boolean = TestBasicConnectivity()
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT: Initial connectivity test: " & testResult.ToString())
            Catch testEx As Exception
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT: Initial connectivity test failed: " & testEx.Message)
            End Try

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT: Constructor error: " & ex.Message)
            Throw New Exception("Failed to initialize EmailServiceClient: " & ex.Message, ex)
        End Try
    End Sub

#Region "Password Reset Methods"    ''' <summary>
    ''' Request password reset for end user
    ''' </summary>
    Public Function RequestPasswordReset(userId As String, email As String, Optional baseUrl As String = Nothing) As EmailServiceResponse
        Try
            Dim safeBaseUrl As String = If(baseUrl, "")
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""Email"":""{1}"",""BaseUrl"":""{2}""}}", userId, email, safeBaseUrl)

            Dim response = PostRequest("/api/password/reset/request", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function

    ''' <summary>
    ''' Validate password reset token
    ''' </summary>
    Public Function ValidateResetToken(token As String) As EmailServiceResponse
        Try
            Dim response = GetRequest("/api/password/reset/validate/" & token)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function

    ''' <summary>
    ''' Complete password reset with new password
    ''' </summary>
    Public Function CompletePasswordReset(token As String, newPassword As String) As EmailServiceResponse
        Try
            Dim jsonData As String = String.Format("{{""Token"":""{0}"",""NewPassword"":""{1}""}}", token, newPassword)

            Dim response = PostRequest("/api/password/reset/complete", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function

    ''' <summary>
    ''' Send password reset email with temporary password
    ''' </summary>
    Public Function SendPasswordResetEmail(userId As String, email As String, userName As String, tempPassword As String) As EmailServiceResponse
        Try
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""Email"":""{1}"",""UserName"":""{2}"",""TempPassword"":""{3}""}}", 
                                                  userId, email, userName, tempPassword)

            Dim response = PostRequest("/api/password/reset/send", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function    ''' <summary>
    ''' Admin create password for user
    ''' </summary>
    Public Function AdminCreatePassword(userId As String, email As String, tempPassword As String, adminId As String) As EmailServiceResponse
        Try
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""Email"":""{1}"",""TempPassword"":""{2}"",""AdminId"":""{3}""}}", userId, email, tempPassword, adminId)

            Dim response = PostRequest("/api/password/admin/create", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function    ''' <summary>
    ''' Admin reset password for user
    ''' </summary>
    Public Function AdminResetPassword(userId As String, email As String, adminId As String, tempPassword As String) As EmailServiceResponse
        Try
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""Email"":""{1}"",""AdminId"":""{2}"",""TempPassword"":""{3}""}}", userId, email, adminId, tempPassword)

            Dim response = PostRequest("/api/password/admin/reset", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function

#End Region

#Region "OTP Methods"    ''' <summary>
    ''' Generate and send OTP
    ''' </summary>
    Public Function GenerateOTP(userId As String, email As String, purpose As String) As EmailServiceResponse
        Try
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""Email"":""{1}"",""Purpose"":""{2}""}}", userId, email, purpose.ToUpper())

            Dim response = PostRequest("/api/otp/generate", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function    ''' <summary>
    ''' Validate OTP
    ''' </summary>
    Public Function ValidateOTP(userId As String, otpCode As String, purpose As String) As EmailServiceResponse
        Try
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""OtpCode"":""{1}"",""Purpose"":""{2}""}}", userId, otpCode, purpose.ToUpper())

            Dim response = PostRequest("/api/otp/validate", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            Return result
        End Try
    End Function

#End Region

#Region "Notification Methods"    ''' <summary>
    ''' Send admin notification email
    ''' </summary>
    Public Function SendAdminNotification(action As String, details As String, Optional userId As String = "") As EmailServiceResponse
        Try
            Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            Dim jsonData As String = String.Format("{{""Action"":""{0}"",""Details"":""{1}"",""UserId"":""{2}"",""Timestamp"":""{3}""}}", action, details, userId, timestamp)

            Dim response = PostRequest("/api/notifications/admin", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat menghantar notifikasi admin: " & ex.Message
            Return result
        End Try
    End Function    ''' <summary>
    ''' Send password change notification
    ''' </summary>
    Public Function SendPasswordChangeNotification(userId As String, email As String, Optional details As String = "") As EmailServiceResponse
        Try
            Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            Dim jsonData As String = String.Format("{{""UserId"":""{0}"",""Email"":""{1}"",""Details"":""{2}"",""Timestamp"":""{3}""}}", userId, email, details, timestamp)

            Dim response = PostRequest("/api/notifications/password-change", jsonData)
            Return ParseResponse(response)
        Catch ex As Exception
            Dim result As New EmailServiceResponse()
            result.Success = False
            result.Message = "Ralat menghantar notifikasi perubahan kata laluan: " & ex.Message
            Return result
        End Try
    End Function

    ''' <summary>
    ''' Check if email service is healthy and responding (with retry logic)
    ''' </summary>
    Public Function CheckHealth() As Boolean
        Dim maxRetries As Integer = 3
        Dim retryDelay As Integer = 1000 ' 1 second

        For attempt As Integer = 1 To maxRetries
            Try
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Health check attempt " & attempt & " of " & maxRetries)
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Base URL: " & _baseUrl)

                ' Try the correct health endpoint
                Dim response = GetRequest("/health")
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Health response: '" & response & "'")

                Dim isNotEmpty = Not String.IsNullOrEmpty(response)
                Dim containsHealthy = response.Contains("healthy")
                Dim containsOk = response.Contains("ok")
                Dim isHealthy = isNotEmpty AndAlso (containsHealthy OrElse containsOk)

                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Response not empty: " & isNotEmpty.ToString())
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Contains 'healthy': " & containsHealthy.ToString())
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Contains 'ok': " & containsOk.ToString())
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Final health status: " & isHealthy.ToString())

                If isHealthy Then
                    System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Health check PASSED on attempt " & attempt)
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Health check FAILED on attempt " & attempt & " - Invalid response")
                    If attempt < maxRetries Then
                        System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Retrying in " & retryDelay & "ms...")
                        System.Threading.Thread.Sleep(retryDelay)
                    End If
                End If
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Health check EXCEPTION on attempt " & attempt & ": " & ex.Message)
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Exception type: " & ex.GetType().Name)
                If ex.InnerException IsNot Nothing Then
                    System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Inner exception: " & ex.InnerException.Message)
                End If

                If attempt < maxRetries Then
                    System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Retrying in " & retryDelay & "ms...")
                    System.Threading.Thread.Sleep(retryDelay)
                Else
                    System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: All retry attempts failed")
                End If
            End Try
        Next

        System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Health check FAILED after all attempts")
        Return False
    End Function

    ''' <summary>
    ''' Simple health check without API key (fallback method)
    ''' </summary>
    Public Function CheckHealthSimple() As Boolean
        Try
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health check (no API key)...")

            Dim url As String = _baseUrl & "/health"
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health check URL: " & url)

            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)

            request.Method = "GET"
            request.Timeout = 5000 ' 5 second timeout

            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health check status: " & response.StatusCode.ToString())

                If response.StatusCode = Net.HttpStatusCode.OK Then
                    Using responseStream As Stream = response.GetResponseStream()
                        Using reader As New StreamReader(responseStream)
                            Dim responseText As String = reader.ReadToEnd()
                            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health response: '" & responseText & "'")
                            Dim isHealthy As Boolean = responseText.Contains("healthy")
                            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health check result: " & isHealthy.ToString())
                            Return isHealthy
                        End Using
                    End Using
                End If
            End Using

            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health check failed - non-OK status")
            Return False
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Simple health check exception: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Exception type: " & ex.GetType().Name)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Test basic connectivity to the service (very simple test)
    ''' </summary>
    Public Function TestConnectivity() As Boolean
        Try
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Testing basic connectivity...")

            Dim url As String = _baseUrl & "/health"
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)

            request.Method = "GET"
            request.Timeout = 3000 ' 3 second timeout for quick test

            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Connectivity test successful - Status: " & response.StatusCode.ToString())
                Return True
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Connectivity test failed: " & ex.Message)
            Return False
        End Try
    End Function

#End Region

#Region "HTTP Helper Methods"

    Private Function PostRequest(endpoint As String, jsonData As String) As String
        Dim url As String = _baseUrl & endpoint
        Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
        
        request.Method = "POST"
        request.ContentType = "application/json"
        request.Headers.Add("X-API-Key", _apiKey)
        request.Timeout = _timeout
        
        ' Write JSON data
        Dim dataBytes As Byte() = Encoding.UTF8.GetBytes(jsonData)
        request.ContentLength = dataBytes.Length
        
        Using requestStream As Stream = request.GetRequestStream()
            requestStream.Write(dataBytes, 0, dataBytes.Length)
        End Using
        
        ' Get response
        Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
            Using responseStream As Stream = response.GetResponseStream()
                Using reader As New StreamReader(responseStream)
                    Return reader.ReadToEnd()
                End Using
            End Using
        End Using
    End Function

    Private Function GetRequest(endpoint As String) As String
        Try
            Dim url As String = _baseUrl & endpoint
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: GET Request URL: " & url)

            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)

            request.Method = "GET"
            request.Headers.Add("X-API-Key", _apiKey)
            request.Timeout = _timeout

            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Request created with API key")

            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Response status: " & response.StatusCode.ToString())

                Using responseStream As Stream = response.GetResponseStream()
                    Using reader As New StreamReader(responseStream)
                        Dim responseText As String = reader.ReadToEnd()
                        System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: Response text: '" & responseText & "'")
                        Return responseText
                    End Using
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("EMAIL CLIENT DEBUG: GetRequest exception: " & ex.Message)
            Throw ex
        End Try
    End Function

    Private Function ParseResponse(jsonResponse As String) As EmailServiceResponse
        ' Simple JSON parsing for .NET 3.5 compatibility
        Dim result As New EmailServiceResponse()
        
        Try
            If jsonResponse.Contains("""success"":true") OrElse jsonResponse.Contains("""Success"":true") Then
                result.Success = True
            Else
                result.Success = False
            End If
            
            ' Extract message
            Dim messageStart As Integer = jsonResponse.IndexOf("""message"":")
            If messageStart = -1 Then messageStart = jsonResponse.IndexOf("""Message"":")
            
            If messageStart > -1 Then
                messageStart = jsonResponse.IndexOf("""", messageStart + 10) + 1
                Dim messageEnd As Integer = jsonResponse.IndexOf("""", messageStart)
                If messageEnd > messageStart Then
                    result.Message = jsonResponse.Substring(messageStart, messageEnd - messageStart)
                End If
            End If
            
            ' Extract data
            Dim dataStart As Integer = jsonResponse.IndexOf("""data"":")
            If dataStart = -1 Then dataStart = jsonResponse.IndexOf("""Data"":")
            
            If dataStart > -1 Then
                dataStart = jsonResponse.IndexOf("""", dataStart + 7) + 1
                Dim dataEnd As Integer = jsonResponse.IndexOf("""", dataStart)
                If dataEnd > dataStart Then
                    result.Data = jsonResponse.Substring(dataStart, dataEnd - dataStart)
                End If
            End If
            
        Catch ex As Exception
            result.Success = False
            result.Message = "Ralat memproses respons: " & ex.Message
        End Try
        
        Return result
    End Function

#End Region

End Class

''' <summary>
''' Response model for email service operations (.NET 3.5 Compatible)
''' </summary>
Public Class EmailServiceResponse
    Private _success As Boolean
    Private _message As String = ""
    Private _data As String = ""

    Public Property Success() As Boolean
        Get
            Return _success
        End Get
        Set(value As Boolean)
            _success = value
        End Set
    End Property

    Public Property Message() As String
        Get
            Return _message
        End Get
        Set(value As String)
            _message = value
        End Set
    End Property

    Public Property Data() As String
        Get
            Return _data
        End Get
        Set(value As String)
            _data = value
        End Set
    End Property
End Class
