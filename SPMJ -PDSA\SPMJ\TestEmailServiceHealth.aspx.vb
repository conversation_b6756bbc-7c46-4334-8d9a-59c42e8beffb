Imports System.Configuration

Partial Public Class TestEmailServiceHealth
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            litDebugInfo.Text = "Page loaded. Ready to test email service health."
        End If
    End Sub

    Protected Sub btnTestHealth_Click(sender As Object, e As EventArgs) Handles btnTestHealth.Click
        Dim debugMessages As New System.Text.StringBuilder()
        
        Try
            debugMessages.AppendLine("=== Email Service Health Check Test ===<br/>")
            
            ' Get service URL from config
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            debugMessages.AppendLine("Service URL: " & serviceUrl & "<br/>")
            
            ' Create EmailServiceClient
            debugMessages.AppendLine("Creating EmailServiceClient...<br/>")
            Dim emailClient As New EmailServiceClient(serviceUrl)
            
            debugMessages.AppendLine("EmailServiceClient created successfully.<br/>")
            
            ' Test health check
            debugMessages.AppendLine("Calling CheckHealth()...<br/>")
            Dim isHealthy As Boolean = emailClient.CheckHealth()
            
            debugMessages.AppendLine("CheckHealth() returned: " & isHealthy.ToString() & "<br/>")
            
            If isHealthy Then
                litHealthResult.Text = "<span class='success'>✅ Email Service is HEALTHY</span>"
                debugMessages.AppendLine("Health check result: SUCCESS<br/>")
            Else
                litHealthResult.Text = "<span class='error'>❌ Email Service is NOT HEALTHY</span>"
                debugMessages.AppendLine("Health check result: FAILED<br/>")
            End If
            
            ' Test OTP generation
            debugMessages.AppendLine("<br/>Testing OTP generation...<br/>")
            Try
                Dim otpResponse = emailClient.GenerateOTP("testuser", "<EMAIL>", "LOGIN")
                debugMessages.AppendLine("OTP Generation Success: " & otpResponse.Success.ToString() & "<br/>")
                debugMessages.AppendLine("OTP Generation Message: " & otpResponse.Message & "<br/>")
            Catch otpEx As Exception
                debugMessages.AppendLine("OTP Generation Error: " & otpEx.Message & "<br/>")
            End Try
            
        Catch ex As Exception
            litHealthResult.Text = "<span class='error'>❌ Error: " & ex.Message & "</span>"
            debugMessages.AppendLine("Exception occurred: " & ex.Message & "<br/>")
            debugMessages.AppendLine("Stack trace: " & ex.StackTrace.Replace(vbCrLf, "<br/>") & "<br/>")
        End Try
        
        litDebugInfo.Text = debugMessages.ToString()
    End Sub

End Class
